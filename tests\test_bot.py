"""
Test suite for Financial News Translation Bot
"""
import unittest
import asyncio
import sys
import os

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from config import get_config
from src.translation_engine import TranslationEngine
from src.news_analyzer import NewsAnalyzer
from src.message_formatter import MessageFormatter

class TestTranslationEngine(unittest.TestCase):
    """Test translation engine functionality"""
    
    def setUp(self):
        self.config = get_config('development')
        self.engine = TranslationEngine(self.config)
    
    def test_financial_terms_loading(self):
        """Test if financial terms are loaded correctly"""
        self.assertIsInstance(self.engine.financial_terms, dict)
        self.assertGreater(len(self.engine.financial_terms), 0)
    
    def test_preprocess_text(self):
        """Test text preprocessing"""
        text = "Apple $AAPL reported 15% growth in Q3 earnings"
        processed, protected = self.engine.preprocess_text(text)
        
        self.assertIn('__SYMBOL_', processed)
        self.assertIn('__NUMBER_', processed)
        self.assertGreater(len(protected), 0)
    
    def test_translation(self):
        """Test basic translation functionality"""
        text = "The Federal Reserve raised interest rates"
        translation = self.engine.translate_text(text)
        
        self.assertIsInstance(translation, str)
        self.assertNotEqual(translation, text)  # Should be different from original
    
    def test_number_conversion(self):
        """Test Persian number conversion"""
        text = "123.45%"
        persian_text = self.engine.convert_numbers_to_persian(text)
        
        self.assertIn('۱۲۳', persian_text)
        self.assertIn('۴۵', persian_text)

class TestNewsAnalyzer(unittest.TestCase):
    """Test news analyzer functionality"""
    
    def setUp(self):
        self.config = get_config('development')
        self.analyzer = NewsAnalyzer(self.config)
    
    def test_key_information_extraction(self):
        """Test extraction of key information"""
        text = "Apple $AAPL reported 15% growth in Q3 earnings, beating expectations"
        info = self.analyzer.extract_key_information(text)
        
        self.assertIn('market_symbols', info)
        self.assertIn('numbers_and_percentages', info)
        self.assertIn('sentiment_indicators', info)
        
        # Check if symbols are detected
        self.assertIn('$AAPL', info['market_symbols'])
        
        # Check if percentages are detected
        self.assertTrue(any('15%' in item for item in info['numbers_and_percentages']))
    
    def test_sentiment_determination(self):
        """Test sentiment analysis"""
        positive_text = "Apple reported strong growth and beat earnings expectations"
        negative_text = "Apple stock crashed after disappointing earnings report"
        neutral_text = "Apple will report earnings next week"
        
        # Test positive sentiment
        info = self.analyzer.extract_key_information(positive_text)
        sentiment, confidence = self.analyzer.determine_sentiment(positive_text, info)
        self.assertEqual(sentiment, 'positive')
        
        # Test negative sentiment
        info = self.analyzer.extract_key_information(negative_text)
        sentiment, confidence = self.analyzer.determine_sentiment(negative_text, info)
        self.assertEqual(sentiment, 'negative')
    
    def test_analysis_generation(self):
        """Test analysis generation"""
        original = "Federal Reserve raises interest rates by 0.25%"
        translated = "فدرال رزرو نرخ بهره را ۰.۲۵% افزایش داد"
        
        analysis = self.analyzer.analyze_news(original, translated)
        
        self.assertIn('analysis', analysis)
        self.assertIn('sentiment', analysis)
        self.assertIn('confidence', analysis)
        self.assertIsInstance(analysis['analysis'], str)

class TestMessageFormatter(unittest.TestCase):
    """Test message formatter functionality"""
    
    def setUp(self):
        self.config = get_config('development')
        self.formatter = MessageFormatter(self.config)
    
    def test_emoji_loading(self):
        """Test if emojis are loaded correctly"""
        self.assertIsInstance(self.formatter.emojis, dict)
        self.assertIn('up', self.formatter.emojis)
        self.assertIn('down', self.formatter.emojis)
    
    def test_sentiment_emoji(self):
        """Test sentiment emoji selection"""
        up_emoji = self.formatter.get_sentiment_emoji('positive')
        down_emoji = self.formatter.get_sentiment_emoji('negative')
        neutral_emoji = self.formatter.get_sentiment_emoji('neutral')
        
        self.assertEqual(up_emoji, '📈')
        self.assertEqual(down_emoji, '📉')
        self.assertEqual(neutral_emoji, '➡️')
    
    def test_message_formatting(self):
        """Test complete message formatting"""
        original_text = "Apple reports strong Q3 earnings"
        translated_text = "اپل گزارش درآمد قوی سه‌ماهه سوم ارائه داد"
        
        analysis_data = {
            'analysis': 'این خبر مثبت برای سهامداران اپل است',
            'sentiment': 'positive',
            'confidence': 0.8,
            'key_info': {
                'market_symbols': ['$AAPL'],
                'financial_terms': ['earnings']
            }
        }
        
        tweet_data = {
            'url': 'https://twitter.com/test/status/123',
            'username': 'test_user'
        }
        
        message = self.formatter.create_professional_message(
            original_text, translated_text, analysis_data, tweet_data
        )
        
        self.assertIsInstance(message, str)
        self.assertIn(translated_text, message)
        self.assertIn('تحلیل', message)
        self.assertIn('منبع', message)
    
    def test_message_validation(self):
        """Test message format validation"""
        valid_message = "*Bold text* with proper formatting"
        invalid_message = "*Unmatched bold formatting"
        
        # Note: We need to create a mock telegram bot for this test
        # For now, we'll test the basic validation logic
        self.assertTrue(len(valid_message) <= 4096)

class TestIntegration(unittest.TestCase):
    """Integration tests"""
    
    def setUp(self):
        self.config = get_config('development')
    
    def test_full_pipeline(self):
        """Test the complete processing pipeline"""
        # Sample tweet data
        tweet_data = {
            'id': '123456789',
            'text': 'Apple $AAPL reports 15% growth in Q3 earnings, beating analyst expectations',
            'url': 'https://twitter.com/test/status/123456789',
            'username': 'test_user'
        }
        
        # Initialize components
        translation_engine = TranslationEngine(self.config)
        news_analyzer = NewsAnalyzer(self.config)
        message_formatter = MessageFormatter(self.config)
        
        # Step 1: Translate
        translated_text = translation_engine.translate_text(tweet_data['text'])
        self.assertIsInstance(translated_text, str)
        
        # Step 2: Analyze
        analysis_data = news_analyzer.analyze_news(tweet_data['text'], translated_text)
        self.assertIn('analysis', analysis_data)
        
        # Step 3: Format
        formatted_message = message_formatter.create_professional_message(
            tweet_data['text'], translated_text, analysis_data, tweet_data
        )
        self.assertIsInstance(formatted_message, str)
        self.assertGreater(len(formatted_message), 0)

def run_tests():
    """Run all tests"""
    print("🧪 Running Financial News Translation Bot Tests")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestTranslationEngine,
        TestNewsAnalyzer,
        TestMessageFormatter,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("🎉 All tests passed!")
    else:
        print(f"❌ {len(result.failures)} test(s) failed")
        print(f"❌ {len(result.errors)} error(s) occurred")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
