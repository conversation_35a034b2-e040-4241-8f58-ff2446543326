# 🚀 راهنمای سریع راه‌اندازی ربات

## 📋 **مراحل راه‌اندازی:**

### 1️⃣ **تهیه API Keys (از کارفرما)**
کارفرما باید این کلیدها را تهیه کند:

#### **Twitter API Keys:**
- به [developer.twitter.com](https://developer.twitter.com/) مراجعه کنید
- حساب توسعه‌دهنده ایجاد کنید
- پروژه جدید بسازید و کلیدهای زیر را دریافت کنید:
  - `TWITTER_BEARER_TOKEN`
  - `TWITTER_API_KEY`
  - `TWITTER_API_SECRET`
  - `TWITTER_ACCESS_TOKEN`
  - `TWITTER_ACCESS_TOKEN_SECRET`

#### **Telegram Bot Token:**
- به [@BotFather](https://t.me/BotFather) در تلگرام مراجعه کنید
- دستور `/newbot` را ارسال کنید
- نام و username ربات را انتخاب کنید
- `TELEGRAM_BOT_TOKEN` را دریافت کنید
- `TELEGRAM_CHANNEL_ID` کانال خود را پیدا کنید

### 2️⃣ **نصب وابستگی‌ها**
```bash
pip install -r requirements.txt
```

### 3️⃣ **تنظیم فایل .env**
```bash
# کپی کردن فایل نمونه
copy .env.example .env

# ویرایش فایل .env و وارد کردن کلیدهای API
```

### 4️⃣ **تست سیستم**
```bash
python run.py --check
```

### 5️⃣ **اجرای ربات**
```bash
python main.py
```

---

## 💰 **هزینه‌ها:**

### ✅ **رایگان:**
- Google Translate
- MyMemory Translator
- HuggingFace AI Models
- LibreTranslate

### 💳 **نیاز به پرداخت:**
- Twitter API (ممکن است رایگان باشد)
- Telegram Bot (رایگان)
- OpenAI API (اختیاری - استفاده نمی‌شود)

---

## 📱 **قالب خروجی:**

هر پست شامل:
1. 📸 **اسکرین‌شات توییت** (به عنوان عکس)
2. 📝 **ترجمه فارسی**
3. 📌 **پ.ن:** توضیح اصطلاحات مالی
4. 📊 **تحلیل سریع برای تریدرها**
5. 📢 **آیدی کانال**

---

## 🔧 **عیب‌یابی:**

### مشکل: "Twitter API 401 Unauthorized"
**راه‌حل:** بررسی صحت کلیدهای Twitter API

### مشکل: "Telegram Chat not found"
**راه‌حل:** بررسی صحت Channel ID و اضافه کردن ربات به کانال

### مشکل: "Translation failed"
**راه‌حل:** بررسی اتصال اینترنت

---

## 📞 **پشتیبانی:**
در صورت بروز مشکل، فایل `logs/bot.log` را بررسی کنید.
