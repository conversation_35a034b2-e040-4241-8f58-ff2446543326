"""
Quick run script for Financial News Translation Bot
"""
import os
import sys
import asyncio
import argparse
from pathlib import Path

def check_environment():
    """Check if environment is properly set up"""
    print("🔍 Checking environment...")
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        print("📝 Please copy .env.example to .env and fill in your API keys")
        return False
    
    # Check if required directories exist
    required_dirs = ['data', 'logs', 'src']
    for directory in required_dirs:
        if not os.path.exists(directory):
            print(f"❌ Directory {directory} not found!")
            print("🔧 Run: python setup.py")
            return False
    
    print("✅ Environment check passed")
    return True

def check_dependencies():
    """Check if all dependencies are installed"""
    print("📦 Checking dependencies...")
    
    required_modules = [
        'tweepy', 'telegram', 'openai', 'googletrans', 
        'deep_translator', 'loguru', 'requests', 'python_dotenv'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module.replace('-', '_'))
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ Missing modules: {', '.join(missing_modules)}")
        print("📦 Run: pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies installed")
    return True

def test_configuration():
    """Test bot configuration"""
    print("⚙️ Testing configuration...")
    
    try:
        from config import get_config
        config = get_config()
        
        # Check required variables
        required_vars = [
            ('TWITTER_BEARER_TOKEN', 'Twitter Bearer Token'),
            ('TELEGRAM_BOT_TOKEN', 'Telegram Bot Token'),
            ('TELEGRAM_CHANNEL_ID', 'Telegram Channel ID'),
            ('TARGET_TWITTER_USERNAME', 'Target Twitter Username')
        ]
        
        missing_vars = []
        for var_name, var_desc in required_vars:
            if not getattr(config, var_name, None):
                missing_vars.append(var_desc)
        
        if missing_vars:
            print(f"❌ Missing configuration: {', '.join(missing_vars)}")
            print("📝 Please update your .env file")
            return False
        
        print("✅ Configuration is valid")
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

async def test_connections():
    """Test API connections"""
    print("🔗 Testing API connections...")
    
    try:
        # Add src to path
        sys.path.append('src')
        
        from config import get_config
        from src.twitter_bot import TwitterBot
        from src.telegram_bot import TelegramBot
        
        config = get_config()
        
        # Test Twitter connection
        print("  📱 Testing Twitter API...")
        try:
            twitter_bot = TwitterBot(config)
            print("  ✅ Twitter API connected")
        except Exception as e:
            print(f"  ❌ Twitter API failed: {e}")
            return False
        
        # Test Telegram connection
        print("  📢 Testing Telegram API...")
        try:
            telegram_bot = TelegramBot(config)
            connection_ok = await telegram_bot.test_connection()
            if connection_ok:
                print("  ✅ Telegram API connected")
            else:
                print("  ❌ Telegram API connection failed")
                return False
        except Exception as e:
            print(f"  ❌ Telegram API failed: {e}")
            return False
        
        print("✅ All API connections successful")
        return True
        
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False

def run_tests():
    """Run unit tests"""
    print("🧪 Running tests...")
    
    try:
        from tests.test_bot import run_tests
        success = run_tests()
        return success
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        return False

async def start_bot():
    """Start the bot"""
    print("🚀 Starting Financial News Translation Bot...")
    
    try:
        from main import main
        await main()
    except KeyboardInterrupt:
        print("\n⏹️ Bot stopped by user")
    except Exception as e:
        print(f"❌ Bot crashed: {e}")
        sys.exit(1)

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Financial News Translation Bot Runner')
    parser.add_argument('--setup', action='store_true', help='Run setup only')
    parser.add_argument('--test', action='store_true', help='Run tests only')
    parser.add_argument('--check', action='store_true', help='Check environment only')
    parser.add_argument('--force', action='store_true', help='Skip checks and start bot')
    
    args = parser.parse_args()
    
    print("🤖 Financial News Translation Bot")
    print("=" * 50)
    
    # Setup mode
    if args.setup:
        print("🔧 Running setup...")
        os.system('python setup.py')
        return
    
    # Test mode
    if args.test:
        success = run_tests()
        sys.exit(0 if success else 1)
    
    # Check mode
    if args.check:
        checks = [
            check_environment(),
            check_dependencies(),
            test_configuration()
        ]
        
        if all(checks):
            print("\n🎉 All checks passed! Bot is ready to run.")
            print("▶️ Run: python run.py")
        else:
            print("\n❌ Some checks failed. Please fix the issues above.")
            sys.exit(1)
        return
    
    # Normal startup
    if not args.force:
        # Run all checks
        checks = [
            check_environment(),
            check_dependencies(),
            test_configuration()
        ]
        
        if not all(checks):
            print("\n❌ Pre-flight checks failed!")
            print("🔧 Run: python run.py --check")
            sys.exit(1)
        
        # Test connections
        connection_ok = asyncio.run(test_connections())
        if not connection_ok:
            print("\n❌ Connection tests failed!")
            print("📝 Please check your API keys in .env file")
            sys.exit(1)
    
    # Start the bot
    print("\n" + "=" * 50)
    asyncio.run(start_bot())

if __name__ == "__main__":
    main()
