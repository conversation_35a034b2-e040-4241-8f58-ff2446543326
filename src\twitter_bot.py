"""
Twitter Bot Module for Financial News Translation Bot
Handles Twitter monitoring WITHOUT API - Uses web scraping
"""
import requests
import asyncio
import time
import sqlite3
import re
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from loguru import logger
from config import Config
from bs4 import BeautifulSoup

class TwitterBot:
    """Twitter Bot for monitoring tweets WITHOUT API - Uses FREE web scraping"""

    def __init__(self, config: Config):
        self.config = config
        self.session = requests.Session()
        self.last_tweet_id = None
        self.processed_tweets = set()
        self.setup_session()
        self.setup_database()
    
    def setup_session(self):
        """Initialize web scraping session - NO API REQUIRED"""
        try:
            # Setup headers to mimic a real browser
            self.session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            })

            logger.info("Web scraping session initialized - NO API REQUIRED")

        except Exception as e:
            logger.error(f"Failed to setup session: {e}")
            raise
    
    def setup_database(self):
        """Setup SQLite database for tracking processed tweets"""
        try:
            conn = sqlite3.connect(self.config.DATABASE_PATH)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS processed_tweets (
                    tweet_id TEXT PRIMARY KEY,
                    username TEXT,
                    content TEXT,
                    created_at TIMESTAMP,
                    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            logger.info("Database setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup database: {e}")
            raise
    
    def is_tweet_processed(self, tweet_id: str) -> bool:
        """Check if tweet has already been processed"""
        try:
            conn = sqlite3.connect(self.config.DATABASE_PATH)
            cursor = conn.cursor()
            
            cursor.execute(
                "SELECT tweet_id FROM processed_tweets WHERE tweet_id = ?",
                (tweet_id,)
            )
            result = cursor.fetchone()
            conn.close()
            
            return result is not None
            
        except Exception as e:
            logger.error(f"Error checking processed tweet: {e}")
            return False
    
    def mark_tweet_processed(self, tweet_id: str, username: str, content: str, created_at: datetime):
        """Mark tweet as processed in database"""
        try:
            conn = sqlite3.connect(self.config.DATABASE_PATH)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO processed_tweets 
                (tweet_id, username, content, created_at)
                VALUES (?, ?, ?, ?)
            ''', (tweet_id, username, content, created_at))
            
            conn.commit()
            conn.close()
            logger.debug(f"Tweet {tweet_id} marked as processed")
            
        except Exception as e:
            logger.error(f"Error marking tweet as processed: {e}")
    
    def scrape_twitter_profile(self, username: str) -> List[Dict]:
        """Scrape Twitter profile using web scraping - NO API REQUIRED"""
        try:
            # Use nitter.net as a free alternative to Twitter API
            url = f"https://nitter.net/{username}"

            response = self.session.get(url, timeout=10)
            if response.status_code != 200:
                logger.warning(f"Failed to access {url}, status: {response.status_code}")
                return []

            soup = BeautifulSoup(response.content, 'html.parser')
            tweets = []

            # Find tweet containers
            tweet_containers = soup.find_all('div', class_='timeline-item')

            for container in tweet_containers[:5]:  # Get latest 5 tweets
                try:
                    # Extract tweet text
                    tweet_content = container.find('div', class_='tweet-content')
                    if not tweet_content:
                        continue

                    text = tweet_content.get_text(strip=True)
                    if not text or len(text) < 10:  # Skip very short tweets
                        continue

                    # Extract tweet ID from link
                    tweet_link = container.find('a', class_='tweet-link')
                    tweet_id = None
                    if tweet_link:
                        href = tweet_link.get('href', '')
                        tweet_id = href.split('/')[-1] if '/' in href else str(hash(text))
                    else:
                        tweet_id = str(hash(text))  # Fallback ID

                    # Check if already processed
                    if self.is_tweet_processed(tweet_id):
                        continue

                    # Extract timestamp
                    time_elem = container.find('span', class_='tweet-date')
                    created_at = datetime.now()  # Fallback to current time

                    tweet_data = {
                        'id': tweet_id,
                        'text': text,
                        'created_at': created_at,
                        'username': username,
                        'url': f"https://twitter.com/{username}/status/{tweet_id}",
                        'screenshot_url': f"https://nitter.net/{username}/status/{tweet_id}"
                    }

                    tweets.append(tweet_data)
                    logger.debug(f"Scraped tweet: {text[:50]}...")

                except Exception as e:
                    logger.warning(f"Error parsing tweet container: {e}")
                    continue

            logger.info(f"Scraped {len(tweets)} new tweets from @{username}")
            return tweets

        except Exception as e:
            logger.error(f"Error scraping Twitter profile {username}: {e}")
            return []

    def get_latest_tweets(self, username: str, count: int = 10) -> List[Dict]:
        """Get latest tweets using FREE web scraping - NO API REQUIRED"""
        try:
            logger.info(f"Scraping latest tweets from @{username} - NO API REQUIRED")

            # Use web scraping instead of API
            scraped_tweets = self.scrape_twitter_profile(username)

            if not scraped_tweets:
                logger.info(f"No new tweets found for @{username}")
                return []

            processed_tweets = []
            for tweet_data in scraped_tweets:
                # Filter financial content
                if not self.is_financial_content(tweet_data['text']):
                    continue

                processed_tweets.append(tweet_data)

                # Mark as processed
                self.mark_tweet_processed(
                    tweet_data['id'],
                    username,
                    tweet_data['text'],
                    tweet_data['created_at']
                )

            logger.info(f"Found {len(processed_tweets)} new financial tweets")
            return processed_tweets
            
        except Exception as e:
            logger.error(f"Error getting latest tweets: {e}")
            return []
    
    def monitor_user_tweets(self, username: str, callback_func) -> None:
        """Monitor user tweets in real-time"""
        logger.info(f"Starting to monitor tweets from {username}")
        
        while True:
            try:
                new_tweets = self.get_latest_tweets(username, count=5)
                
                for tweet in new_tweets:
                    logger.info(f"New tweet detected: {tweet['id']}")
                    # Call the callback function to process the tweet
                    asyncio.create_task(callback_func(tweet))
                
                # Wait before next check (avoid rate limiting)
                time.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Error in tweet monitoring: {e}")
                time.sleep(300)  # Wait 5 minutes on error
    
    def get_tweet_by_id(self, tweet_id: str) -> Optional[Dict]:
        """Get specific tweet by ID"""
        try:
            tweet = self.client.get_tweet(
                id=tweet_id,
                tweet_fields=['created_at', 'public_metrics', 'context_annotations', 'entities']
            )
            
            if tweet.data:
                return {
                    'id': tweet.data.id,
                    'text': tweet.data.text,
                    'created_at': tweet.data.created_at,
                    'url': f"https://twitter.com/i/status/{tweet_id}",
                    'metrics': tweet.data.public_metrics if hasattr(tweet.data, 'public_metrics') else {},
                    'entities': tweet.data.entities if hasattr(tweet.data, 'entities') else {}
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting tweet {tweet_id}: {e}")
            return None
    
    def search_tweets(self, query: str, count: int = 10) -> List[Dict]:
        """Search for tweets with specific query"""
        try:
            tweets = self.client.search_recent_tweets(
                query=query,
                max_results=count,
                tweet_fields=['created_at', 'public_metrics', 'context_annotations', 'entities']
            )
            
            if not tweets.data:
                return []
            
            processed_tweets = []
            for tweet in tweets.data:
                tweet_data = {
                    'id': tweet.id,
                    'text': tweet.text,
                    'created_at': tweet.created_at,
                    'url': f"https://twitter.com/i/status/{tweet.id}",
                    'metrics': tweet.public_metrics if hasattr(tweet, 'public_metrics') else {},
                    'entities': tweet.entities if hasattr(tweet, 'entities') else {}
                }
                processed_tweets.append(tweet_data)
            
            return processed_tweets
            
        except Exception as e:
            logger.error(f"Error searching tweets: {e}")
            return []
