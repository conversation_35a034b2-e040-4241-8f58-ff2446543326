# 🤖 ربات مترجم اخبار مالی

ربات هوشمند برای ترجمه و انتشار لحظه‌ای اخبار مالی از توییتر به تلگرام با دقت بالا در اصطلاحات تخصصی.

## ✨ ویژگی‌های کلیدی

- **ترجمه هوشمند**: ترکیب OpenAI GPT و Google Translate با دیکشنری ۲۵۰۰+ اصطلاح مالی
- **تحلیل خودکار**: تولید تحلیل کوتاه و حرفه‌ای از هر خبر
- **فرمت‌بندی حرفه‌ای**: پیام‌های زیبا با ایموجی و فرمت مارک‌داون
- **پایش لحظه‌ای**: دریافت و پردازش توییت‌ها در کمتر از ۲ ثانیه
- **مدیریت خطا**: سیستم بازیابی خودکار و لاگ‌گیری کامل

## 🏗️ معماری سیستم

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Twitter API   │───▶│  Translation     │───▶│  News Analysis  │
│                 │    │  Engine          │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Telegram Bot   │◀───│  Message         │◀───│  Data Storage   │
│                 │    │  Formatter       │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 📋 پیش‌نیازها

- Python 3.8+
- Twitter Developer Account
- Telegram Bot Token
- OpenAI API Key (اختیاری برای کیفیت بهتر)

## 🚀 نصب و راه‌اندازی

### 1. کلون کردن پروژه
```bash
git clone <repository-url>
cd motarjemtoiter
```

### 2. نصب وابستگی‌ها
```bash
pip install -r requirements.txt
```

### 3. تنظیم متغیرهای محیطی
```bash
cp .env.example .env
```

فایل `.env` را ویرایش کنید:
```env
# Twitter API
TWITTER_BEARER_TOKEN=your_bearer_token
TWITTER_API_KEY=your_api_key
TWITTER_API_SECRET=your_api_secret
TWITTER_ACCESS_TOKEN=your_access_token
TWITTER_ACCESS_TOKEN_SECRET=your_access_token_secret

# Telegram
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHANNEL_ID=@your_channel

# OpenAI (اختیاری)
OPENAI_API_KEY=your_openai_key

# هدف
TARGET_TWITTER_USERNAME=financialjuice
```

### 4. راه‌اندازی
```bash
python main.py
```

## 📊 نمونه خروجی

```
📰 تصویر نویت: "S&P: China's stable outlook reflects that economy will return to self-sustaining growth of above 4% over the next few years."

🔄 ترجمه:
اس‌اند‌پی: چشم‌انداز پایدار چین نشان‌دهنده بازگشت اقتصاد به رشد خودپایدار بالای ۴% طی سال‌های آینده است.

📊 تحلیل:
این خبر نشان‌دهنده اعتماد آژانس‌های رتبه‌بندی به بهبود اقتصاد چین است. رشد ۴% برای دومین اقتصاد جهان مثبت محسوب می‌شود.

📌 پ.ن:
اصطلاحات کلیدی: GDP, Economic Growth, Rating Agency

🌍 منبع: @financialjuice
⏰ زمان انتشار: ۱۴:۳۰
```

## ⚙️ تنظیمات پیشرفته

### فایل `config.py`
```python
# تنظیمات ترجمه
TRANSLATION_LANGUAGE = 'fa'  # فارسی
USE_EMOJIS = True
USE_BOLD_TEXT = True
INCLUDE_ANALYSIS = True

# تنظیمات پایش
TWITTER_RATE_LIMIT_WINDOW = 900  # ۱۵ دقیقه
TWITTER_RATE_LIMIT_REQUESTS = 300
```

### اضافه کردن اصطلاحات جدید
فایل `data/financial_terms.json` را ویرایش کنید:
```json
{
  "financial_terms": {
    "Quantitative Easing": "تسهیل کمی",
    "اصطلاح جدید": "ترجمه فارسی"
  }
}
```

## 🧪 تست سیستم

### تست اتصالات
```bash
python -c "
import asyncio
from main import FinancialNewsBot
bot = FinancialNewsBot()
asyncio.run(bot.initialize_components())
print('✅ همه اتصالات موفق')
"
```

### تست ترجمه
```bash
python -c "
from src.translation_engine import TranslationEngine
from config import get_config
engine = TranslationEngine(get_config())
result = engine.translate_text('The Federal Reserve raised interest rates by 0.25%')
print(f'ترجمه: {result}')
"
```

## 📈 مانیتورینگ و آمار

ربات آمار زیر را ارائه می‌دهد:
- تعداد توییت‌های پردازش شده
- تعداد ترجمه‌های موفق
- تعداد پیام‌های ارسالی
- نرخ خطا
- زمان فعالیت

## 🔧 عیب‌یابی

### مشکلات رایج

1. **خطای Twitter API**
   ```
   Error: 401 Unauthorized
   ```
   **راه‌حل**: بررسی صحت کلیدهای API

2. **خطای Telegram**
   ```
   Error: Chat not found
   ```
   **راه‌حل**: اطمینان از صحت Channel ID

3. **خطای ترجمه**
   ```
   Translation failed
   ```
   **راه‌حل**: بررسی اتصال اینترنت و کلید OpenAI

### لاگ‌ها
لاگ‌ها در `logs/bot.log` ذخیره می‌شوند.

## 🛡️ امنیت

- همه کلیدهای API در فایل `.env` نگهداری می‌شوند
- لاگ‌ها حاوی اطلاعات حساس نیستند
- پایگاه داده محلی برای ذخیره توییت‌های پردازش شده

## 🤝 مشارکت

1. Fork کنید
2. Branch جدید بسازید (`git checkout -b feature/amazing-feature`)
3. تغییرات را commit کنید (`git commit -m 'Add amazing feature'`)
4. Push کنید (`git push origin feature/amazing-feature`)
5. Pull Request ایجاد کنید

## 📄 مجوز

این پروژه تحت مجوز MIT منتشر شده است.

## 📞 پشتیبانی

- **تلگرام**: @your_support_username
- **ایمیل**: <EMAIL>
- **مستندات**: [لینک مستندات]

## 🔄 به‌روزرسانی

برای دریافت آخرین به‌روزرسانی‌ها:
```bash
git pull origin main
pip install -r requirements.txt --upgrade
```

---

**ساخته شده با ❤️ برای جامعه مالی ایران**
