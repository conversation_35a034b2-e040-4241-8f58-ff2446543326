# 🚀 راهنمای سریع راه‌اندازی

## ۱. نصب وابستگی‌ها
```bash
pip install -r requirements.txt
```

## ۲. تنظیم API Keys

### Twitter API
1. برو به [Twitter Developer Portal](https://developer.twitter.com/)
2. یک App جدید بساز
3. کلیدهای زیر رو بگیر:
   - Bearer Token
   - API Key & Secret
   - Access Token & Secret

### Telegram Bot
1. به [@BotFather](https://t.me/BotFather) پیام بده
2. دستور `/newbot` رو بفرست
3. نام و username ربات رو انتخاب کن
4. Bot Token رو کپی کن

### OpenAI (اختیاری)
1. برو به [OpenAI Platform](https://platform.openai.com/)
2. API Key بساز

## ۳. تنظیم فایل محیطی
```bash
cp .env.example .env
```

فایل `.env` رو ویرایش کن:
```env
TWITTER_BEARER_TOKEN=AAAAAAAAAAAAAAAAAAAAMLheAAAAAAA0%2BuSeid...
TWITTER_API_KEY=KjdIJDL77...
TWITTER_API_SECRET=BjdIJDL77...
TWITTER_ACCESS_TOKEN=14157...
TWITTER_ACCESS_TOKEN_SECRET=BjdIJDL77...

TELEGRAM_BOT_TOKEN=5872441624:AAHdqTcvCH1vGWJxfSeofSAs0K5PALDsaw
TELEGRAM_CHANNEL_ID=@your_channel

OPENAI_API_KEY=sk-proj-dedcfE...

TARGET_TWITTER_USERNAME=financialjuice
```

## ۴. راه‌اندازی
```bash
# نصب و تنظیم
python setup.py

# تست سیستم
python run.py --check

# اجرای ربات
python run.py
```

## ۵. تست سریع
```bash
# تست ترجمه
python -c "
from src.translation_engine import TranslationEngine
from config import get_config
engine = TranslationEngine(get_config())
print(engine.translate_text('Apple reports strong earnings'))
"

# تست کامل
python run.py --test
```

## 🔧 عیب‌یابی سریع

### خطای Twitter API
```
Error: 401 Unauthorized
```
**راه‌حل**: بررسی کلیدهای Twitter در `.env`

### خطای Telegram
```
Error: Chat not found
```
**راه‌حل**: 
1. ربات رو به کانال اضافه کن
2. Admin کن
3. Channel ID رو درست وارد کن (`@channel` یا `-100123456789`)

### خطای ترجمه
```
Translation failed
```
**راه‌حل**: بررسی اتصال اینترنت و کلید OpenAI

## 📊 مانیتورینگ

### لاگ‌ها
```bash
tail -f logs/bot.log
```

### آمار
ربات آمار زنده در تلگرام نمایش می‌دهد.

## ⚡ دستورات مفید

```bash
# راه‌اندازی سریع
python run.py

# بررسی وضعیت
python run.py --check

# اجرای تست‌ها
python run.py --test

# راه‌اندازی بدون بررسی
python run.py --force

# نصب مجدد
python setup.py
```

## 🎯 نکات مهم

1. **کانال تلگرام**: ربات باید Admin باشه
2. **Twitter Rate Limit**: حداکثر ۳۰۰ درخواست در ۱۵ دقیقه
3. **OpenAI**: برای کیفیت بهتر ترجمه (اختیاری)
4. **لاگ‌ها**: همیشه `logs/bot.log` رو چک کن

## 📞 کمک

اگه مشکلی داشتی:
1. لاگ‌ها رو چک کن
2. `python run.py --check` اجرا کن
3. مستندات کامل رو بخون: `README.md`
