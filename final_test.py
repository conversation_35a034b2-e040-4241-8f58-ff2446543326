"""
تست نهایی ربات - آماده اجرا
"""
import asyncio
from telegram import Bot

async def final_test():
    """Final test with real settings"""
    try:
        print("🚀 FINAL TEST - Bot Ready Check")
        print("=" * 50)
        
        # Bot settings
        bot_token = "**********:AAHL0OJoGCgehj3eQEF1Kh6HJmREKhe-vvw"
        channel_id = "@FinancialJuiceFarsi"
        twitter_account = "@financialjuice"
        
        print(f"🤖 Bot Token: {bot_token[:20]}...")
        print(f"📢 Channel: {channel_id}")
        print(f"🐦 Twitter: {twitter_account}")
        
        # Create bot
        bot = Bot(token=bot_token)
        
        # Test 1: Bot info
        print(f"\n1️⃣ Testing bot connection...")
        bot_info = await bot.get_me()
        print(f"✅ Bot Name: {bot_info.first_name}")
        print(f"✅ Bot Username: @{bot_info.username}")
        
        # Test 2: Channel access
        print(f"\n2️⃣ Testing channel access...")
        try:
            chat = await bot.get_chat(channel_id)
            print(f"✅ Channel found: {chat.title}")
            print(f"✅ Channel type: {chat.type}")
            
            # Test 3: Send test message
            print(f"\n3️⃣ Sending test message...")
            test_message = """🤖 *تست ربات مترجم اخبار مالی*

📝 *ترجمه فارسی توییت:*
این یک پیام تست است برای بررسی عملکرد ربات

📌 *پ.ن:*
*Test:* این یک تست است

📊 *تحلیل سریع برای تریدرها:*
• *تست سیستم:* موفقیت‌آمیز

📢 @FinancialJuiceFarsi

⏰ *زمان تست:* اکنون"""
            
            message = await bot.send_message(
                chat_id=channel_id,
                text=test_message,
                parse_mode='Markdown'
            )
            
            print(f"✅ Test message sent successfully!")
            print(f"✅ Message ID: {message.message_id}")
            print(f"✅ Message link: https://t.me/{channel_id.replace('@', '')}/{message.message_id}")
            
            # Test 4: Delete test message (cleanup)
            print(f"\n4️⃣ Cleaning up test message...")
            await asyncio.sleep(2)  # Wait 2 seconds
            await bot.delete_message(chat_id=channel_id, message_id=message.message_id)
            print(f"✅ Test message deleted")
            
            return True
            
        except Exception as e:
            print(f"❌ Channel/Message error: {e}")
            
            if "not enough rights" in str(e).lower():
                print("💡 Solution: Make sure bot has 'Post Messages' permission")
            elif "chat not found" in str(e).lower():
                print("💡 Solution: Make sure bot is added to the channel")
            
            return False
            
    except Exception as e:
        print(f"❌ Bot connection error: {e}")
        return False

async def main():
    """Main test"""
    success = await final_test()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 SUCCESS! Bot is 100% ready!")
        print("\n📋 Next steps:")
        print("1. Run: python main.py")
        print("2. Bot will start monitoring @financialjuice")
        print("3. Translated posts will appear in @FinancialJuiceFarsi")
        print("\n💰 Cost: FREE (No paid APIs)")
        print("🔄 Status: Ready to go live!")
        
    else:
        print("❌ FAILED! Please check bot permissions")
        print("\n🔧 Troubleshooting:")
        print("1. Make sure bot is added to @FinancialJuiceFarsi")
        print("2. Make sure bot is Admin with 'Post Messages' permission")
        print("3. Try the test again")

if __name__ == "__main__":
    asyncio.run(main())
