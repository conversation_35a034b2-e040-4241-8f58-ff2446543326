"""
تست دستی - ارسال پیام نمونه به کانال
"""
import asyncio
from telegram import Bot

async def send_sample_post():
    """Send a sample financial news post"""
    try:
        bot = Bot('8207363120:AAHL0OJoGCgehj3eQEF1Kh6HJmREKhe-vvw')
        channel_id = '@FinancialJuiceFarsi'
        
        # Sample financial news post
        sample_post = """📝 *ترجمه فارسی توییت:*
فدرال رزرو آمریکا نرخ بهره را ۰.۲۵ درصد افزایش داد تا با تورم مبارزه کند

📌 *پ.ن:*
*Fed:* بانک مرکزی آمریکا - تصمیم‌گیرنده اصلی نرخ بهره
*تورم:* افزایش عمومی قیمت‌ها در اقتصاد

📊 *تحلیل سریع برای تریدرها:*
• *دلار (DXY):* احتمال تقویت کوتاه‌مدت
• *طلا (XAU/USD):* فشار نزولی احتمالی
• *یورو/دلار:* نیاز به پیگیری واکنش ECB

📢 @FinancialJuiceFarsi"""
        
        print("📤 Sending sample post...")
        message = await bot.send_message(
            chat_id=channel_id,
            text=sample_post,
            parse_mode='Markdown'
        )
        
        print(f"✅ Sample post sent!")
        print(f"📱 Check: https://t.me/FinancialJuiceFarsi/{message.message_id}")
        print(f"🆔 Message ID: {message.message_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Manual Test - Sample Post")
    print("=" * 40)
    
    result = asyncio.run(send_sample_post())
    
    if result:
        print("\n🎉 Success! Check the channel to see the post")
    else:
        print("\n❌ Failed! Check bot permissions")
