"""
Simple test to verify basic functionality
"""
import sys
import os

def test_basic_imports():
    """Test basic Python imports"""
    print("🧪 Testing basic imports...")
    
    try:
        import json
        print("✅ json")
        
        import re
        print("✅ re")
        
        import datetime
        print("✅ datetime")
        
        import sqlite3
        print("✅ sqlite3")
        
        return True
    except Exception as e:
        print(f"❌ Basic import failed: {e}")
        return False

def test_file_structure():
    """Test if all files exist"""
    print("\n📁 Testing file structure...")
    
    required_files = [
        'config.py',
        'main.py',
        'src/__init__.py',
        'src/translation_engine.py',
        'src/news_analyzer.py',
        'src/message_formatter.py',
        'src/twitter_bot.py',
        'src/telegram_bot.py',
        'data/financial_terms.json'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def test_config_loading():
    """Test config loading"""
    print("\n⚙️ Testing config loading...")
    
    try:
        sys.path.append('.')
        from config import get_config
        
        config = get_config()
        print("✅ Config loaded successfully")
        
        # Test some basic attributes
        if hasattr(config, 'TRANSLATION_LANGUAGE'):
            print(f"✅ Translation language: {config.TRANSLATION_LANGUAGE}")
        
        if hasattr(config, 'TARGET_TWITTER_USERNAME'):
            print(f"✅ Target username: {config.TARGET_TWITTER_USERNAME}")
        
        return True
        
    except Exception as e:
        print(f"❌ Config loading failed: {e}")
        return False

def test_financial_terms():
    """Test financial terms loading"""
    print("\n💰 Testing financial terms...")
    
    try:
        import json
        
        with open('data/financial_terms.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        financial_terms = data.get('financial_terms', {})
        market_symbols = data.get('market_symbols', {})
        emojis = data.get('emojis', {})
        
        print(f"✅ Financial terms loaded: {len(financial_terms)} terms")
        print(f"✅ Market symbols loaded: {len(market_symbols)} symbols")
        print(f"✅ Emojis loaded: {len(emojis)} emojis")
        
        # Test some specific terms
        if 'Federal Reserve' in financial_terms:
            print(f"✅ Federal Reserve → {financial_terms['Federal Reserve']}")
        
        if '$AAPL' in market_symbols:
            print(f"✅ $AAPL → {market_symbols['$AAPL']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Financial terms test failed: {e}")
        return False

def test_translation_engine():
    """Test translation engine basic functionality"""
    print("\n🔄 Testing translation engine...")
    
    try:
        sys.path.append('src')
        from config import get_config
        from src.translation_engine import TranslationEngine
        
        config = get_config()
        engine = TranslationEngine(config)
        
        print("✅ Translation engine created")
        
        # Test preprocessing
        text = "Apple $AAPL reports 15% growth"
        processed, protected = engine.preprocess_text(text)
        
        print(f"✅ Preprocessing works")
        print(f"   Original: {text}")
        print(f"   Processed: {processed}")
        print(f"   Protected terms: {len(protected)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Translation engine test failed: {e}")
        return False

def main():
    """Run simple tests"""
    print("🤖 Simple Bot Test")
    print("=" * 40)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("File Structure", test_file_structure),
        ("Config Loading", test_config_loading),
        ("Financial Terms", test_financial_terms),
        ("Translation Engine", test_translation_engine)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name}...")
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results.append(False)
    
    print("\n" + "=" * 40)
    print("📊 Test Results:")
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ PASS" if results[i] else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    passed = sum(results)
    total = len(results)
    
    print(f"\n🎯 Summary: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All basic tests passed!")
        print("\n📝 Next steps:")
        print("1. Set up API keys in .env file")
        print("2. Install remaining dependencies")
        print("3. Run full tests")
    else:
        print("❌ Some tests failed. Check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
