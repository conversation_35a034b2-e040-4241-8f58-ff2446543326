"""
Translation Engine Module for Financial News Translation Bot
Handles intelligent translation with financial terms preservation
Uses FREE translation services only - NO PAID APIs
"""
import json
import re
import requests
from typing import Dict, List, Tuple, Optional
from googletrans import Translator
from deep_translator import GoogleTranslator, MyMemoryTranslator, LibreTranslator
from loguru import logger
from config import Config

class TranslationEngine:
    """Intelligent translation engine with financial terms support"""
    
    def __init__(self, config: Config):
        self.config = config
        self.financial_terms = {}
        self.market_symbols = {}
        self.emojis = {}

        # FREE Translation Services Setup
        self.google_translator = GoogleTranslator(source='en', target='fa')
        self.backup_translator = Translator()
        self.mymemory_translator = MyMemoryTranslator(source='en', target='fa')

        # Try to setup LibreTranslate (free alternative)
        try:
            self.libre_translator = LibreTranslator(source='en', target='fa', api_key=None)
        except:
            self.libre_translator = None
            logger.warning("LibreTranslate not available")

        self.load_financial_terms()
    
    def load_financial_terms(self):
        """Load financial terms dictionary"""
        try:
            with open(self.config.FINANCIAL_TERMS_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.financial_terms = data.get('financial_terms', {})
                self.market_symbols = data.get('market_symbols', {})
                self.emojis = data.get('emojis', {})
            
            logger.info(f"Loaded {len(self.financial_terms)} financial terms")
            
        except Exception as e:
            logger.error(f"Error loading financial terms: {e}")
            self.financial_terms = {}
            self.market_symbols = {}
            self.emojis = {}
    
    def preprocess_text(self, text: str) -> Tuple[str, Dict[str, str]]:
        """Preprocess text to protect financial terms and symbols"""
        protected_terms = {}
        processed_text = text
        
        # Protect market symbols (e.g., $TSLA, $AAPL)
        symbol_pattern = r'\$[A-Z]{1,5}'
        symbols = re.findall(symbol_pattern, text)
        for i, symbol in enumerate(symbols):
            placeholder = f"__SYMBOL_{i}__"
            protected_terms[placeholder] = symbol
            processed_text = processed_text.replace(symbol, placeholder)
        
        # Protect financial terms
        for term, translation in self.financial_terms.items():
            if term.lower() in text.lower():
                placeholder = f"__TERM_{len(protected_terms)}__"
                protected_terms[placeholder] = term
                # Case-insensitive replacement
                pattern = re.compile(re.escape(term), re.IGNORECASE)
                processed_text = pattern.sub(placeholder, processed_text)
        
        # Protect numbers with % and currency symbols
        number_pattern = r'[\d,]+\.?\d*\s*%|[\d,]+\.?\d*\s*\$|[\d,]+\.?\d*\s*€'
        numbers = re.findall(number_pattern, text)
        for i, number in enumerate(numbers):
            placeholder = f"__NUMBER_{i}__"
            protected_terms[placeholder] = number
            processed_text = processed_text.replace(number, placeholder)
        
        return processed_text, protected_terms
    
    def postprocess_text(self, translated_text: str, protected_terms: Dict[str, str]) -> str:
        """Restore protected terms in translated text"""
        result = translated_text
        
        for placeholder, original_term in protected_terms.items():
            if placeholder.startswith('__SYMBOL_'):
                # Replace market symbols with Persian equivalent if available
                persian_symbol = self.market_symbols.get(original_term, original_term)
                result = result.replace(placeholder, persian_symbol)
            elif placeholder.startswith('__TERM_'):
                # Replace with Persian financial term
                persian_term = self.financial_terms.get(original_term, original_term)
                result = result.replace(placeholder, persian_term)
            elif placeholder.startswith('__NUMBER_'):
                # Convert numbers to Persian format
                persian_number = self.convert_numbers_to_persian(original_term)
                result = result.replace(placeholder, persian_number)
            else:
                result = result.replace(placeholder, original_term)
        
        return result
    
    def convert_numbers_to_persian(self, text: str) -> str:
        """Convert English numbers to Persian"""
        english_to_persian = {
            '0': '۰', '1': '۱', '2': '۲', '3': '۳', '4': '۴',
            '5': '۵', '6': '۶', '7': '۷', '8': '۸', '9': '۹'
        }
        
        result = text
        for eng, per in english_to_persian.items():
            result = result.replace(eng, per)
        
        return result
    
    def translate_with_free_ai(self, text: str) -> Optional[str]:
        """Translate using FREE AI services (Hugging Face, etc.)"""
        try:
            # Try Hugging Face free translation API
            url = "https://api-inference.huggingface.co/models/Helsinki-NLP/opus-mt-en-fa"
            headers = {"Authorization": "Bearer hf_demo"}  # Free demo token

            payload = {"inputs": text}
            response = requests.post(url, headers=headers, json=payload, timeout=10)

            if response.status_code == 200:
                result = response.json()
                if isinstance(result, list) and len(result) > 0:
                    translation = result[0].get('translation_text', '')
                    if translation:
                        logger.debug(f"HuggingFace translation: {translation}")
                        return translation

            logger.warning("HuggingFace translation failed, trying alternative...")
            return None

        except Exception as e:
            logger.error(f"Free AI translation error: {e}")
            return None

    def translate_with_mymemory(self, text: str) -> Optional[str]:
        """Translate using MyMemory (free service)"""
        try:
            translation = self.mymemory_translator.translate(text)
            logger.debug(f"MyMemory translation: {translation}")
            return translation

        except Exception as e:
            logger.warning(f"MyMemory translation error: {e}")
            return None
    
    def translate_with_google(self, text: str) -> Optional[str]:
        """Translate using Google Translate"""
        try:
            translation = self.google_translator.translate(text)
            logger.debug(f"Google translation: {translation}")
            return translation
            
        except Exception as e:
            logger.warning(f"Google Translate error: {e}")
            try:
                # Fallback to backup translator
                translation = self.backup_translator.translate(text, src='en', dest='fa')
                return translation.text
            except Exception as e2:
                logger.error(f"Backup translation error: {e2}")
                return None
    
    def translate_text(self, text: str) -> str:
        """Main translation function with intelligent processing"""
        try:
            logger.info(f"Translating text: {text[:100]}...")
            
            # Preprocess to protect financial terms
            processed_text, protected_terms = self.preprocess_text(text)
            
            # Try FREE AI services first for better quality
            translation = self.translate_with_free_ai(processed_text)

            # Fallback to MyMemory (free service)
            if not translation:
                translation = self.translate_with_mymemory(processed_text)

            # Fallback to Google Translate
            if not translation:
                translation = self.translate_with_google(processed_text)
            
            if not translation:
                logger.error("All translation methods failed")
                return text  # Return original text if translation fails
            
            # Postprocess to restore protected terms
            final_translation = self.postprocess_text(translation, protected_terms)
            
            # Clean up the translation
            final_translation = self.clean_translation(final_translation)
            
            logger.info(f"Translation completed: {final_translation[:100]}...")
            return final_translation
            
        except Exception as e:
            logger.error(f"Translation error: {e}")
            return text
    
    def clean_translation(self, text: str) -> str:
        """Clean and improve translation quality"""
        # Remove extra spaces
        text = re.sub(r'\s+', ' ', text).strip()
        
        # Fix common translation issues
        replacements = {
            'درصد': '%',
            'دلار آمریکا': 'دلار',
            'ایالات متحده آمریکا': 'آمریکا',
            'بازار سهام': 'بازار',
        }
        
        for old, new in replacements.items():
            text = text.replace(old, new)
        
        return text
    
    def get_translation_confidence(self, original: str, translated: str) -> float:
        """Calculate translation confidence score"""
        try:
            # Simple confidence calculation based on length ratio and term preservation
            length_ratio = len(translated) / len(original) if len(original) > 0 else 0
            
            # Check if financial terms are preserved
            financial_terms_found = 0
            total_financial_terms = 0
            
            for term in self.financial_terms.keys():
                if term.lower() in original.lower():
                    total_financial_terms += 1
                    if self.financial_terms[term] in translated:
                        financial_terms_found += 1
            
            term_preservation_score = (
                financial_terms_found / total_financial_terms 
                if total_financial_terms > 0 else 1.0
            )
            
            # Combine scores
            confidence = (
                min(length_ratio, 1.0) * 0.3 +  # Length similarity
                term_preservation_score * 0.7    # Term preservation
            )
            
            return min(confidence, 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating confidence: {e}")
            return 0.5
