"""
Setup script for Financial News Translation Bot
"""
import os
import sys
import subprocess
from pathlib import Path

def create_directories():
    """Create necessary directories"""
    directories = ['data', 'logs', 'src', 'tests']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✓ Directory created: {directory}")

def install_requirements():
    """Install Python requirements"""
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✓ Requirements installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install requirements: {e}")
        return False
    return True

def setup_environment():
    """Setup environment file"""
    env_example = '.env.example'
    env_file = '.env'
    
    if not os.path.exists(env_file):
        if os.path.exists(env_example):
            import shutil
            shutil.copy(env_example, env_file)
            print(f"✓ Environment file created: {env_file}")
            print("⚠️  Please edit .env file with your API keys")
        else:
            print("✗ .env.example not found")
            return False
    else:
        print("✓ Environment file already exists")
    
    return True

def test_imports():
    """Test if all required modules can be imported"""
    required_modules = [
        'tweepy', 'telegram', 'openai', 'googletrans', 
        'deep_translator', 'loguru', 'requests', 'asyncio'
    ]
    
    failed_imports = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✓ {module}")
        except ImportError:
            print(f"✗ {module}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\n⚠️  Failed to import: {', '.join(failed_imports)}")
        print("Please run: pip install -r requirements.txt")
        return False
    
    print("✓ All required modules imported successfully")
    return True

def validate_config():
    """Validate configuration"""
    try:
        from config import get_config
        config = get_config()
        
        required_vars = [
            'TWITTER_BEARER_TOKEN', 'TELEGRAM_BOT_TOKEN', 
            'TELEGRAM_CHANNEL_ID', 'TARGET_TWITTER_USERNAME'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not getattr(config, var, None):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"⚠️  Missing required environment variables: {', '.join(missing_vars)}")
            print("Please update your .env file")
            return False
        
        print("✓ Configuration validated")
        return True
        
    except Exception as e:
        print(f"✗ Configuration validation failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 Setting up Financial News Translation Bot")
    print("=" * 50)
    
    steps = [
        ("Creating directories", create_directories),
        ("Installing requirements", install_requirements),
        ("Setting up environment", setup_environment),
        ("Testing imports", test_imports),
        ("Validating configuration", validate_config)
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            print(f"❌ Setup failed at: {step_name}")
            sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 Setup completed successfully!")
    print("\n📝 Next steps:")
    print("1. Edit .env file with your API keys")
    print("2. Run: python main.py")
    print("3. Check logs/bot.log for detailed logs")
    print("\n📚 Documentation: README.md")

if __name__ == "__main__":
    main()
