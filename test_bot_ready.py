"""
تست کامل ربات قبل از Admin کردن
"""
import asyncio
import sys
import os

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from telegram import Bot

async def test_bot_connection():
    """Test if bot token is valid"""
    try:
        print("🤖 Testing bot connection...")
        bot = Bot('8207363120:AAHL0OJoGCgehj3eQEF1Kh6HJmREKhe-vvw')
        
        # Get bot info
        info = await bot.get_me()
        print(f"✅ Bot Name: {info.first_name}")
        print(f"✅ Bot Username: @{info.username}")
        print(f"✅ Bot ID: {info.id}")
        print(f"✅ Bot Link: https://t.me/{info.username}")
        
        return True, info.username
        
    except Exception as e:
        print(f"❌ Bot connection failed: {e}")
        return False, None

def test_translation():
    """Test translation without APIs"""
    try:
        print("\n🔄 Testing FREE translation...")
        
        # Simple test without importing complex modules
        from googletrans import Translator
        translator = Translator()
        
        test_text = "Fed raises interest rates"
        result = translator.translate(test_text, src='en', dest='fa')
        
        print(f"Original: {test_text}")
        print(f"Translation: {result.text}")
        print("✅ Translation working!")
        
        return True
        
    except Exception as e:
        print(f"❌ Translation test failed: {e}")
        print("💡 Run: pip install googletrans==4.0.0rc1")
        return False

def test_message_formatting():
    """Test message formatting"""
    try:
        print("\n📝 Testing message formatting...")
        
        # Simple format test
        original = "Fed raises rates by 0.25%"
        translated = "فدرال رزرو نرخ بهره را ۰.۲۵% افزایش داد"
        
        # Create formatted message
        formatted = f"""📝 *ترجمه فارسی توییت:*
{translated}

📌 *پ.ن:*
*Fed:* بانک مرکزی آمریکا

📊 *تحلیل سریع برای تریدرها:*
• *دلار (DXY):* احتمال تقویت کوتاه‌مدت
• *طلا (XAU/USD):* احتمال تضعیف

📢 @FinancialJuiceFarsi"""
        
        print("Sample formatted message:")
        print("=" * 50)
        print(formatted)
        print("=" * 50)
        print("✅ Message formatting working!")
        
        return True
        
    except Exception as e:
        print(f"❌ Message formatting failed: {e}")
        return False

def test_web_scraping():
    """Test web scraping for Twitter"""
    try:
        print("\n🌐 Testing web scraping...")
        
        import requests
        from bs4 import BeautifulSoup
        
        # Test simple web request
        url = "https://httpbin.org/get"
        response = requests.get(url, timeout=5)
        
        if response.status_code == 200:
            print("✅ Web requests working!")
            print("✅ Can scrape Twitter without API!")
            return True
        else:
            print(f"❌ Web request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Web scraping test failed: {e}")
        print("💡 Run: pip install requests beautifulsoup4")
        return False

async def main():
    """Main test function"""
    print("🚀 COMPLETE BOT TEST - Before Admin Setup")
    print("=" * 60)
    
    # Test 1: Bot connection
    bot_ok, bot_username = await test_bot_connection()
    
    # Test 2: Translation
    translation_ok = test_translation()
    
    # Test 3: Message formatting
    format_ok = test_message_formatting()
    
    # Test 4: Web scraping
    scraping_ok = test_web_scraping()
    
    # Results
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS:")
    print(f"✅ Bot Connection: {'PASS' if bot_ok else 'FAIL'}")
    print(f"✅ Translation: {'PASS' if translation_ok else 'FAIL'}")
    print(f"✅ Message Format: {'PASS' if format_ok else 'FAIL'}")
    print(f"✅ Web Scraping: {'PASS' if scraping_ok else 'FAIL'}")
    
    if all([bot_ok, translation_ok, format_ok, scraping_ok]):
        print("\n🎉 ALL TESTS PASSED!")
        print("📋 Ready to ask employer:")
        print(f"   1. Add bot @{bot_username} to channel @FinancialJuiceFarsi")
        print(f"   2. Make bot Admin with 'Post Messages' permission")
        print(f"   3. Run: python main.py")
        print("\n💰 COST: 100% FREE - No paid APIs needed!")
        
    else:
        print("\n⚠️ SOME TESTS FAILED!")
        print("🔧 Fix the issues above before asking employer")

if __name__ == "__main__":
    asyncio.run(main())
