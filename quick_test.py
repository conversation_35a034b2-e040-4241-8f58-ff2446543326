#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import sys
import os

print("🤖 Quick Bot Test")
print("=" * 30)

# Test 1: Financial Terms
print("\n💰 Testing Financial Terms...")
try:
    with open('data/financial_terms.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    terms = data.get('financial_terms', {})
    symbols = data.get('market_symbols', {})
    emojis = data.get('emojis', {})
    
    print(f"✅ {len(terms)} financial terms loaded")
    print(f"✅ {len(symbols)} market symbols loaded")
    print(f"✅ {len(emojis)} emojis loaded")
    
    # Test specific terms
    if 'Federal Reserve' in terms:
        print(f"✅ Federal Reserve → {terms['Federal Reserve']}")
    
    if '$AAPL' in symbols:
        print(f"✅ $AAPL → {symbols['$AAPL']}")
        
except Exception as e:
    print(f"❌ Financial terms test failed: {e}")

# Test 2: Config
print("\n⚙️ Testing Config...")
try:
    from config import get_config
    config = get_config()
    
    print(f"✅ Config loaded")
    print(f"✅ Target: {config.TARGET_TWITTER_USERNAME}")
    print(f"✅ Language: {config.TRANSLATION_LANGUAGE}")
    
except Exception as e:
    print(f"❌ Config test failed: {e}")

# Test 3: Translation Engine (basic)
print("\n🔄 Testing Translation Engine...")
try:
    sys.path.append('src')
    from src.translation_engine import TranslationEngine
    
    engine = TranslationEngine(config)
    print("✅ Translation engine created")
    
    # Test preprocessing
    text = "Apple $AAPL reports 15% growth"
    processed, protected = engine.preprocess_text(text)
    
    print(f"✅ Preprocessing works")
    print(f"   Original: {text}")
    print(f"   Protected: {len(protected)} terms")
    
except Exception as e:
    print(f"❌ Translation engine test failed: {e}")

# Test 4: Message Formatter
print("\n📝 Testing Message Formatter...")
try:
    from src.message_formatter import MessageFormatter
    
    formatter = MessageFormatter(config)
    print("✅ Message formatter created")
    
    # Test emoji
    emoji = formatter.get_sentiment_emoji('positive')
    print(f"✅ Positive emoji: {emoji}")
    
except Exception as e:
    print(f"❌ Message formatter test failed: {e}")

print("\n" + "=" * 30)
print("🎉 Basic tests completed!")
print("\n📝 Next steps:")
print("1. Install dependencies: pip install tweepy python-telegram-bot")
print("2. Set up .env file with API keys")
print("3. Run full bot: python main.py")
