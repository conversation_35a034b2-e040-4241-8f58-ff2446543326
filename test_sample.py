"""
Sample test script to verify bot functionality
"""
import sys
import os
import asyncio

# Add src to path
sys.path.append('src')

def test_imports():
    """Test if all modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        from config import get_config
        print("✅ Config imported")
        
        from src.translation_engine import TranslationEngine
        print("✅ Translation engine imported")
        
        from src.news_analyzer import NewsAnalyzer
        print("✅ News analyzer imported")
        
        from src.message_formatter import MessageFormatter
        print("✅ Message formatter imported")
        
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_translation():
    """Test translation functionality"""
    print("\n🔄 Testing translation...")
    
    try:
        from config import get_config
        from src.translation_engine import TranslationEngine
        
        config = get_config()
        engine = TranslationEngine(config)
        
        # Test simple translation
        text = "Apple reports strong quarterly earnings"
        translation = engine.translate_text(text)
        
        print(f"Original: {text}")
        print(f"Translation: {translation}")
        
        if translation and translation != text:
            print("✅ Translation working")
            return True
        else:
            print("❌ Translation failed")
            return False
            
    except Exception as e:
        print(f"❌ Translation test failed: {e}")
        return False

def test_analysis():
    """Test news analysis"""
    print("\n📊 Testing analysis...")
    
    try:
        from config import get_config
        from src.news_analyzer import NewsAnalyzer
        
        config = get_config()
        analyzer = NewsAnalyzer(config)
        
        original = "Apple $AAPL reports 15% growth in quarterly earnings"
        translated = "اپل رشد ۱۵ درصدی در درآمد فصلی گزارش داد"
        
        analysis = analyzer.analyze_news(original, translated)
        
        print(f"Analysis: {analysis['analysis']}")
        print(f"Sentiment: {analysis['sentiment']}")
        print(f"Confidence: {analysis['confidence']}")
        
        if analysis['analysis']:
            print("✅ Analysis working")
            return True
        else:
            print("❌ Analysis failed")
            return False
            
    except Exception as e:
        print(f"❌ Analysis test failed: {e}")
        return False

def test_formatting():
    """Test message formatting"""
    print("\n📝 Testing formatting...")
    
    try:
        from config import get_config
        from src.message_formatter import MessageFormatter
        
        config = get_config()
        formatter = MessageFormatter(config)
        
        original = "Apple reports strong earnings"
        translated = "اپل درآمد قوی گزارش داد"
        analysis_data = {
            'analysis': 'این خبر مثبت برای سهامداران است',
            'sentiment': 'positive',
            'confidence': 0.8,
            'key_info': {
                'market_symbols': ['$AAPL'],
                'financial_terms': ['earnings']
            }
        }
        tweet_data = {
            'url': 'https://twitter.com/test/status/123',
            'username': 'test_user'
        }
        
        message = formatter.create_professional_message(
            original, translated, analysis_data, tweet_data
        )
        
        print("Formatted message:")
        print("-" * 40)
        print(message)
        print("-" * 40)
        
        if message and len(message) > 0:
            print("✅ Formatting working")
            return True
        else:
            print("❌ Formatting failed")
            return False
            
    except Exception as e:
        print(f"❌ Formatting test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🤖 Financial News Translation Bot - Sample Test")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("Translation Test", test_translation),
        ("Analysis Test", test_analysis),
        ("Formatting Test", test_formatting)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name}...")
        result = test_func()
        results.append(result)
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ PASS" if results[i] else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    if all(results):
        print("\n🎉 All tests passed! Bot is ready to use.")
        print("\n📝 Next steps:")
        print("1. Set up your .env file with API keys")
        print("2. Run: python run.py --check")
        print("3. Run: python run.py")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
    
    return all(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
