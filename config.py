"""
Configuration file for Financial News Translation Bot
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Main configuration class"""
    
    # Twitter API Configuration
    TWITTER_BEARER_TOKEN = os.getenv('TWITTER_BEARER_TOKEN')
    TWITTER_API_KEY = os.getenv('TWITTER_API_KEY')
    TWITTER_API_SECRET = os.getenv('TWITTER_API_SECRET')
    TWITTER_ACCESS_TOKEN = os.getenv('TWITTER_ACCESS_TOKEN')
    TWITTER_ACCESS_TOKEN_SECRET = os.getenv('TWITTER_ACCESS_TOKEN_SECRET')
    
    # Telegram Bot Configuration
    TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
    TELEGRAM_CHANNEL_ID = os.getenv('TELEGRAM_CHANNEL_ID')  # @your_channel or -100xxxxxxxxx
    
    # OpenAI Configuration (for analysis and translation enhancement)
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    
    # Google Translate API (backup translation service)
    GOOGLE_TRANSLATE_API_KEY = os.getenv('GOOGLE_TRANSLATE_API_KEY')
    
    # Target Twitter Account
    TARGET_TWITTER_USERNAME = os.getenv('TARGET_TWITTER_USERNAME', 'financialjuice')
    
    # Bot Settings
    TRANSLATION_LANGUAGE = 'fa'  # Persian
    SOURCE_LANGUAGE = 'en'      # English
    
    # Message Format Settings
    USE_EMOJIS = True
    USE_BOLD_TEXT = True
    INCLUDE_ANALYSIS = True
    INCLUDE_SCREENSHOT = True
    
    # Financial Terms Dictionary Path
    FINANCIAL_TERMS_FILE = 'data/financial_terms.json'
    
    # Logging Configuration
    LOG_LEVEL = 'INFO'
    LOG_FILE = 'logs/bot.log'
    
    # Database Configuration (for storing processed tweets)
    DATABASE_PATH = 'data/processed_tweets.db'
    
    # Rate Limiting
    TWITTER_RATE_LIMIT_WINDOW = 900  # 15 minutes in seconds
    TWITTER_RATE_LIMIT_REQUESTS = 300
    
    # Translation Settings
    MAX_TRANSLATION_RETRIES = 3
    TRANSLATION_TIMEOUT = 30  # seconds
    
    # Analysis Settings
    ANALYSIS_MAX_LENGTH = 200  # characters
    ANALYSIS_LANGUAGE = 'persian'

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    LOG_LEVEL = 'INFO'

# Configuration mapping
config_map = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

def get_config(config_name='default'):
    """Get configuration based on environment"""
    return config_map.get(config_name, DevelopmentConfig)
