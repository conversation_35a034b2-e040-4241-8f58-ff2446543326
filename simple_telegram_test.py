"""
تست ساده اتصال تلگرام
"""
import asyncio
from telegram import Bot

async def test_telegram():
    """Test Telegram bot connection"""
    try:
        # Your bot token
        bot_token = "8207363120:AAHL0OJoGCgehj3eQEF1Kh6HJmREKhe-vvw"
        channel_id = "@FinancialJuiceFarsi"
        
        print("🤖 Testing Telegram Bot...")
        print(f"Token: {bot_token[:20]}...")
        print(f"Channel: {channel_id}")
        
        # Create bot
        bot = Bot(token=bot_token)
        
        # Test bot info
        print("\n📱 Getting bot info...")
        bot_info = await bot.get_me()
        print(f"✅ Bot Name: {bot_info.first_name}")
        print(f"✅ Bot Username: @{bot_info.username}")
        
        # Test channel access
        print(f"\n📢 Testing channel access...")
        try:
            chat = await bot.get_chat(channel_id)
            print(f"✅ Channel found: {chat.title}")
            print(f"✅ Channel type: {chat.type}")
            
            # Try to send a test message
            print(f"\n📤 Sending test message...")
            test_message = "🤖 تست ربات - این پیام برای تست اتصال است"
            
            message = await bot.send_message(
                chat_id=channel_id,
                text=test_message
            )
            
            print(f"✅ Message sent successfully!")
            print(f"✅ Message ID: {message.message_id}")
            
            return True
            
        except Exception as e:
            print(f"❌ Channel access error: {e}")
            print("\n💡 Solutions:")
            print("1. Make sure the bot is added to the channel")
            print("2. Make sure the bot is an admin in the channel")
            print("3. Check if channel username is correct")
            return False
            
    except Exception as e:
        print(f"❌ Bot connection error: {e}")
        print("\n💡 Check if bot token is correct")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_telegram())
    
    if result:
        print("\n🎉 SUCCESS! Bot is ready to work!")
    else:
        print("\n⚠️ FAILED! Please fix the issues above.")
