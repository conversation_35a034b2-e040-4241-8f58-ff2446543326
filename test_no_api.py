"""
تست ربات بدون نیاز به Twitter API
"""
import asyncio
import sys
import os

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from config import get_config
from src.translation_engine import TranslationEngine
from src.message_formatter import MessageFormatter
from src.telegram_bot import TelegramBot

async def test_translation():
    """Test translation without OpenAI"""
    print("🔄 Testing FREE translation...")
    
    config = get_config()
    translator = TranslationEngine(config)
    
    # Test text
    test_text = "The Federal Reserve raised interest rates by 0.25% to combat inflation"
    
    # Translate
    result = translator.translate_text(test_text)
    print(f"Original: {test_text}")
    print(f"Translation: {result}")
    
    return result

async def test_message_formatting():
    """Test message formatting with new template"""
    print("\n📝 Testing message formatting...")
    
    config = get_config()
    formatter = MessageFormatter(config)
    
    # Sample data
    original_text = "Fed raises rates by 0.25% as inflation concerns persist"
    translated_text = "فدرال رزرو نرخ بهره را ۰.۲۵% افزایش داد زیرا نگرانی‌های تورمی ادامه دارد"
    
    analysis_data = {
        'sentiment': 'neutral',
        'key_info': {
            'financial_terms': ['Fed', 'inflation'],
            'market_symbols': ['DXY']
        }
    }
    
    tweet_data = {
        'id': '123456',
        'username': 'financialjuice',
        'url': 'https://twitter.com/financialjuice/status/123456'
    }
    
    # Format message
    formatted = formatter.create_professional_message(
        original_text, translated_text, analysis_data, tweet_data
    )
    
    print("Formatted message:")
    print("=" * 50)
    print(formatted)
    print("=" * 50)
    
    return formatted

async def test_telegram_connection():
    """Test Telegram bot connection"""
    print("\n📱 Testing Telegram connection...")
    
    try:
        config = get_config()
        telegram_bot = TelegramBot(config)
        
        # Test connection
        connection_ok = await telegram_bot.test_connection()
        
        if connection_ok:
            print("✅ Telegram connection successful!")
            return True
        else:
            print("❌ Telegram connection failed!")
            print("💡 Make sure TELEGRAM_CHANNEL_ID is correct in .env file")
            return False
            
    except Exception as e:
        print(f"❌ Telegram test error: {e}")
        return False

async def main():
    """Main test function"""
    print("🤖 Testing Financial News Bot - NO API REQUIRED")
    print("=" * 60)
    
    # Test translation
    translation_result = await test_translation()
    
    # Test message formatting
    if translation_result:
        message_result = await test_message_formatting()
    
    # Test Telegram (if token is available)
    telegram_result = await test_telegram_connection()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"✅ Translation: {'PASS' if translation_result else 'FAIL'}")
    print(f"✅ Message Format: {'PASS' if 'message_result' in locals() else 'FAIL'}")
    print(f"✅ Telegram: {'PASS' if telegram_result else 'FAIL'}")
    
    if translation_result and telegram_result:
        print("\n🎉 Bot is ready to run!")
        print("▶️ Run: python main.py")
    else:
        print("\n⚠️ Some tests failed. Check the errors above.")

if __name__ == "__main__":
    asyncio.run(main())
